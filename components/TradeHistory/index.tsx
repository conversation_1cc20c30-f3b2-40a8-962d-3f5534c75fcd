"use client";

import React, {
  useRef,
  forwardRef,
  useMemo,
  memo,
  useCallback,
  useState,
} from "react";
import { AppDataTableRealtime } from "@/components";
import {
  CalendarIcon,
  CheckboxIcon,
  CheckboxCheckedIcon,
} from "@/assets/icons";
import { useMediaQuery } from "react-responsive";
import { useWindowSize, useHistoryFilter } from "@/hooks";
import rf from "@/services/RequestFactory";
import { TUserTrade } from "@/types/trade";
import { SelectSideOrder } from "../OrderExchange/components/SelectSideOrder";
import { HistoryFilterDesktop, HistoryFilterMobile } from "../HistoryFilter";
import TradeItem from "./components/TradeItem";
import { BROADCAST_EVENTS } from "@/libs/broadcast";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { usePairContext } from "@/app/trade/[symbol]/provider";
import { TYPE_LAYOUT } from "@/constants/common";
import moment from "moment";

export const CustomDateInput = forwardRef<
  HTMLDivElement,
  { value?: any; onClick?: () => void; isInPair: boolean }
>(({ value, onClick, isInPair = false }, ref) => {
  return (
    <div
      onClick={onClick}
      ref={ref}
      className={` text-white-900 border-white-100  flex cursor-pointer items-center gap-1 rounded-[4px] border ${
        isInPair
          ? "bg-white-100  body-sm-medium-12 rounded-[4px] px-1 py-[1px]"
          : "body-md-medium-14 rounded-[6px] p-2"
      }`}
    >
      {value ? value : <span className="text-white-300">Select date</span>}
      <CalendarIcon className={`${isInPair ? "" : "h-[17px] w-[17px]"}`} />
    </div>
  );
});

CustomDateInput.displayName = "CustomDateInput";

export const TableTradeHistory = memo(
  ({ isInPair = false }: { isInPair: boolean }) => {
    // Use shared filter hook
    const { state: filterState, actions: filterActions } = useHistoryFilter({
      includeQuoteAsset: false,
      includeOrderType: false,
      includeSortBy: false,
    });

    const dataTableRef = useRef<any>(null);
    const [isHideOtherPairs, setIsHideOtherPairs] = useState<boolean>(false);

    // Get current pair context when isInPair is true
    const pairContext = isInPair ? usePairContext() : null;

    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
    const { windowHeight } = useWindowSize();
    const { type } = useSelector(
      (state: RootState) => state.metadata.settingsLayout
    );
    const isLayoutAdvanced = type === TYPE_LAYOUT.ADVANCED;

    // Get pair settings from store
    const { activePairSettings } = useSelector(
      (state: RootState) => state.pairSettings
    );

    // Generate base asset options from pair settings
    const baseAssetOptions = useMemo(() => {
      const baseAssets = new Set<string>();
      activePairSettings.forEach((pair: { baseAsset?: string }) => {
        if (pair.baseAsset) {
          baseAssets.add(pair.baseAsset);
        }
      });

      const sortedBaseAssets = Array.from(baseAssets).sort();
      const options = sortedBaseAssets.map((asset) => ({
        label: asset,
        value: asset,
      }));

      return [{ label: "All", value: "" }, ...options];
    }, [activePairSettings]);

    const quoteAssetOptions = useMemo(() => {
      const quoteAssets = new Set<string>();
      activePairSettings.forEach((pair: { quoteAsset?: string }) => {
        if (pair.quoteAsset) {
          quoteAssets.add(pair.quoteAsset);
        }
      });

      const sortedQuoteAssets = Array.from(quoteAssets).sort();
      const options = sortedQuoteAssets.map((asset) => ({
        label: asset,
        value: asset,
      }));

      return [{ label: "All", value: "" }, ...options];
    }, [activePairSettings]);

    const getMyTrades = useCallback(
      async (params: any) => {
        try {
          // Only fetch data when both start and end dates are provided
          if (!filterState.startDate || !filterState.endDate) {
            return {
              cursor: null,
              data: [],
            };
          }

          const tradeParams = {
            ...params,
            startTime: moment(filterState.startDate).startOf("day").valueOf(),
            endTime: moment(filterState.endDate).endOf("day").valueOf(),
            side: filterState.side,
            baseAsset: filterState.base || undefined, // Include base filter if selected
          };

          // Apply "Hide Other Pairs" filter when enabled and in pair context
          if (isHideOtherPairs && pairContext?.symbol) {
            tradeParams.symbol = pairContext.symbol;
          }

          const { docs, cursor } = await rf
            .getRequest("TradeRequest")
            .getUserTrades(tradeParams);
          return {
            cursor,
            data: docs || [],
          };
        } catch (err) {
          console.log(err, "getMyTrades error");
          return { cursor: null, data: [] };
        }
      },
      [
        filterState.startDate,
        filterState.endDate,
        filterState.side,
        filterState.base,
        isHideOtherPairs,
        pairContext?.symbol,
      ]
    );

    const handleSearch = useCallback(() => {
      if (dataTableRef.current) {
        // Force refresh the data table
        dataTableRef.current.refresh();
      }
    }, []);

    const tableHeight = useMemo(() => {
      if (isInPair) {
        if (isLayoutAdvanced) {
          return 450;
        }

        if (windowHeight > 1310) {
          return windowHeight - 1010;
        }
        return 300;
      }
      if (isMobile) {
        return windowHeight - 50 - 40 - 36;
      }
      return windowHeight - 250;
    }, [windowHeight, isMobile, isInPair, isLayoutAdvanced]);

    return (
      <div className="w-full">
        {!isInPair ? (
          <>
            <HistoryFilterDesktop
              state={filterState}
              actions={filterActions}
              baseAssetOptions={baseAssetOptions}
              quoteAssetOptions={quoteAssetOptions}
              isInPair={isInPair}
              includeQuoteAsset={true}
              includeOrderType={false}
              onSearch={handleSearch}
            />
            <HistoryFilterMobile
              filterState={filterState}
              filterActions={filterActions}
              baseAssetOptions={baseAssetOptions}
              quoteAssetOptions={quoteAssetOptions}
            />
          </>
        ) : (
          <>
            <HistoryFilterDesktop
              state={filterState}
              actions={filterActions}
              baseAssetOptions={baseAssetOptions}
              isInPair={isInPair}
              includeQuoteAsset={false}
              includeOrderType={false}
              onSearch={handleSearch}
            />
            {/* Desktop checkbox for Hide Other Pairs when isInPair */}
            <div className="hidden items-center px-4 py-2 lg:flex">
              <div
                className="body-md-regular-14 flex cursor-pointer items-center gap-2"
                onClick={() => setIsHideOtherPairs(!isHideOtherPairs)}
              >
                {isHideOtherPairs ? <CheckboxCheckedIcon /> : <CheckboxIcon />}
                Hide Other Pairs
              </div>
            </div>
            {/* Mobile checkbox for Hide Other Pairs when isInPair */}
            <div className="flex items-center px-4 py-3 lg:hidden">
              <div
                className="body-md-regular-14 flex items-center gap-2"
                onClick={() => setIsHideOtherPairs(!isHideOtherPairs)}
              >
                {isHideOtherPairs ? <CheckboxCheckedIcon /> : <CheckboxIcon />}
                Hide Other Pairs
              </div>
            </div>
          </>
        )}

        <AppDataTableRealtime
          minWidth={988}
          ref={dataTableRef}
          getData={getMyTrades}
          overrideBodyClassName="w-full"
          handleAddNewItem={{
            broadcastName: BROADCAST_EVENTS.USER_TRADE_UPDATE,
            fieldKey: "id",
            formatter: (data: TUserTrade) => {
              return data;
            },
          }}
          renderHeader={() => {
            if (isMobile) {
              return null;
            }
            return (
              <>
                <div className="flex w-full items-center">
                  <div className="body-sm-regular-12 text-white-500 flex w-[14%] min-w-[100px] items-center px-2 py-1.5 ">
                    Date
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[13%] min-w-[100px] px-2 py-1.5">
                    Pair
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[13%] min-w-[100px] px-2 py-1.5 text-left">
                    <SelectSideOrder
                      side={filterState.side}
                      setSide={filterActions.setSide}
                    />
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[13%] min-w-[100px] px-2 py-1.5 text-left">
                    <div className="flex items-center gap-2">Price</div>
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[13%] min-w-[100px] px-2 py-1.5 text-left">
                    Executed
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[12%] min-w-[100px] px-2 py-1.5 text-left">
                    Fee
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[100px] px-2 py-1.5 text-left">
                    Role
                  </div>
                  <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[100px] px-2 py-1.5 text-left">
                    Total
                  </div>
                  {/* <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[120px] px-2 py-1.5 text-left">
                    Total in USDT
                  </div> */}
                </div>
              </>
            );
          }}
          renderRow={(item: TUserTrade, index: number) => {
            return <TradeItem key={index} trade={item} />;
          }}
          height={tableHeight}
          minHeight={300}
        />
      </div>
    );
  }
);

TableTradeHistory.displayName = "TableTradeHistory";
