"use client";

import React, { useCallback, useState } from "react";
import { TableOpenOrder } from "@/components/OpenOrder";
import { TableOrderHistory } from "@/components/OrderHistory";
import { TableTradeHistory } from "@/components/TradeHistory";
import { CheckboxIcon, CheckboxCheckedIcon } from "@/assets/icons";
import { usePairContext } from "@/app/trade/[symbol]/provider";

enum EBottomSectionTabKeys {
  OPEN_ORDER = "OPEN_ORDER",
  ORDER_HISTORY = "ORDER_HISTORY",
  TRADE_HISTORY = "TRADE_HISTORY",
}

const BOTTOM_SECTION_TABS = [
  {
    label: "Open Order",
    key: EBottomSectionTabKeys.OPEN_ORDER,
  },
  {
    label: "Order History",
    key: EBottomSectionTabKeys.ORDER_HISTORY,
  },
  {
    label: "Trade History",
    key: EBottomSectionTabKeys.TRADE_HISTORY,
  },
];

export const OrdersExchange = ({
  isInPair = false,
}: {
  isInPair?: boolean;
}) => {
  const [bottomSectionTab, setBottomSectionTab] =
    useState<EBottomSectionTabKeys>(EBottomSectionTabKeys.OPEN_ORDER);
  const [isHideOtherPairs, setIsHideOtherPairs] = useState<boolean>(false);

  // Get current pair context when isInPair is true
  const pairContext = isInPair ? usePairContext() : null;

  const renderBottomSectionContent = useCallback(() => {
    if (bottomSectionTab === EBottomSectionTabKeys.OPEN_ORDER) {
      return <TableOpenOrder isInPair={isInPair} />;
    }
    if (bottomSectionTab === EBottomSectionTabKeys.ORDER_HISTORY) {
      return <TableOrderHistory isInPair={isInPair} />;
    }
    if (bottomSectionTab === EBottomSectionTabKeys.TRADE_HISTORY) {
      return <TableTradeHistory isInPair={isInPair} />;
    }
    return <></>;
  }, [bottomSectionTab]);

  return (
    <div
      className={`border-white-100 w-full pt-2.5  ${
        isInPair ? "lg:border-t lg:pt-4" : "lg:pt-0"
      }`}
    >
      <div
        className={`border-white-50 flex items-center justify-between border-b  ${
          isInPair ? "lg:px-4" : ""
        }`}
      >
        <div className="grid w-full grid-cols-3 gap-3 lg:flex">
          {BOTTOM_SECTION_TABS.map((item, index) => {
            return (
              <div
                onClick={() => setBottomSectionTab(item.key)}
                className={`body-md-medium-14 -mb-[1px] cursor-pointer p-2.5 pt-0 text-center lg:p-1 lg:text-left ${
                  item.key === bottomSectionTab
                    ? "text-white-1000 border-white-500 border-b"
                    : "text-white-500"
                }`}
                key={index}
              >
                {item.label}
              </div>
            );
          })}
        </div>
      </div>
      <div className={isInPair ? "lg:px-4" : " "}>
        {renderBottomSectionContent()}
      </div>
    </div>
  );
};
