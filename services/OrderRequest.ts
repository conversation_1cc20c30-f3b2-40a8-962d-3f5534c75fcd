import {
  TOpenOrderParams,
  TOrderHistoryParams,
  TOrderPayload,
  TOrderResponse,
  TPaginationParams,
} from "@/types/order";
import BaseRequest from "./BaseRequest";

export default class OrderRequest extends BaseRequest {
  async placeOrder(payload: TOrderPayload): Promise<TOrderResponse> {
    const url = `/v1/order`;
    return this.post(url, payload);
  }

  getOpenOrders = (params: TOpenOrderParams) => {
    const url = `/v1/openOrders`;
    return this.get(url, params);
  };

  getOrders = (params: TOrderHistoryParams) => {
    const url = `/v1/allOrders`;
    return this.get(url, params);
  };

  cancelOpenOrder = (orderId: string) => {
    const url = `/v1/order?order_id=${orderId}`;
    return this.delete(url);
  };

  cancelAllOpenOrders = () => {
    const url = `/v1/openOrders`;
    return this.delete(url);
  };
}
